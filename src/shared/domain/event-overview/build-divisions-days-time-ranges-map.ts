import { ALL_DIVISIONS_KEY } from './constants';
import {
	DaysMatchesTimeRangesMap,
	DivisionWithTimeRangeRef,
	DivisionsDaysRangesMap,
	MatchesTimeRangeRef,
} from './types';

export const buildDivisionsDaysTimeRangesMap = <
	TR extends MatchesTimeRangeRef = MatchesTimeRangeRef,
>(
	divisions: DivisionWithTimeRangeRef<TR>[],
): DivisionsDaysRangesMap<TR> => {
	const daysSet = new Set<string>();
	const divisionsTimeRanges = new Map<string, DaysMatchesTimeRangesMap<TR>>();
	divisions.forEach(({ division_id, matches_time_ranges }) => {
		const map: DaysMatchesTimeRangesMap<TR> = new Map();
		matches_time_ranges.forEach((timeRange) => {
			const { day } = timeRange;
			daysSet.add(day);
			map.set(day, timeRange);
			divisionsTimeRanges.set(division_id, map);
		});
	});
	const summaryTimeRanges: DaysMatchesTimeRangesMap<TR> = new Map();
	const sortedDays = [...daysSet].sort((a, b) => new Date(a).getTime() - new Date(b).getTime());

	sortedDays.forEach((day) => {
		let startTime = '';
		let endTime = '';
		divisionsTimeRanges.forEach((divisionTimeRanges) => {
			const timeRange = divisionTimeRanges.get(day);
			if (timeRange) {
				const { start_time, end_time } = timeRange;
				startTime = startTime ? (start_time < startTime ? start_time : startTime) : start_time;
				endTime = endTime ? (end_time > endTime ? end_time : endTime) : end_time;
			}
		});
		// NOTE: The summary time ranges contain a limited set of properties, which satisfies the parent type
		summaryTimeRanges.set(day, {
			start_time: startTime,
			end_time: endTime,
			day,
		} as TR);
	});
	divisionsTimeRanges.set(ALL_DIVISIONS_KEY, summaryTimeRanges);
	return divisionsTimeRanges;
};
