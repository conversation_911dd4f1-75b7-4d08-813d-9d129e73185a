import { useCallback, useMemo, useState } from 'react';
import { useNavigate, useParams } from 'react-router';
import styled from 'styled-components';

import { CourtGridBoardWidget } from '@widgets/court-grid-board';

import { useEventOverview } from '@entities/event-overview';

import { DivisionsListOptionsParams, buildDivisionsListOptions } from '@shared/domain/division';
import { ALL_DIVISIONS_KEY } from '@shared/domain/event-overview';

import { useCourtGridParams } from '../model';

const CourtGridPageWrapper = styled.div`
	display: flex;
	flex-direction: column;
	padding: 1rem;
	height: 100vh;
`;

const Title = styled.h2`
	color: ${({ theme }) => theme.colors.primary};
	margin-bottom: 1rem;
`;

const GridContainer = styled.div`
	flex: 1;
	display: flex;
	flex-direction: column;
	min-height: 0;
`;

const TempControls = styled.div`
	display: flex;
	gap: 1rem;
	margin-bottom: 1rem;
	padding: 1rem;
	background: #f8f9fa;
	border-radius: 4px;
	flex-wrap: wrap;
`;

const ControlGroup = styled.div`
	display: flex;
	flex-direction: column;
	gap: 0.5rem;
`;

const Label = styled.label`
	font-size: 12px;
	font-weight: 600;
	color: ${({ theme }) => theme.colors.text};
`;

const Input = styled.input`
	padding: 0.5rem;
	border: 1px solid #dfe3e8;
	border-radius: 4px;
	font-size: 14px;
`;

const Select = styled.select`
	padding: 0.5rem;
	border: 1px solid #dfe3e8;
	border-radius: 4px;
	font-size: 14px;
`;

const DIVISIONS_LIST_OPTIONS_PARAMS: DivisionsListOptionsParams = {
	withAllDivisionOption: true,
	shortName: true,
};

export const CourtGridPage = () => {
	const navigate = useNavigate();
	const { eswId } = useParams();
	const { divisions, loading: eventLoading } = useEventOverview(eswId!);
	const params = useCourtGridParams(divisions);

	const [focusedMatchId, setFocusedMatchId] = useState<string | null>(null);
	const [gridStep, setGridStep] = useState(30);

	const divisionsOptions = useMemo(
		() => (divisions ? buildDivisionsListOptions(divisions, DIVISIONS_LIST_OPTIONS_PARAMS) : []),
		[divisions],
	);

	const updateUrl = useCallback(
		(divisionId: string, dayIndex: number, startTime: string, endTime: string) => {
			navigate(`/event/${eswId}/court-grid/${divisionId}/${dayIndex}/${startTime}/${endTime}`);
		},
		[eswId, navigate],
	);

	const handleDayChange = useCallback(
		(newDay: string) => {
			if (!params) return;
			const { divisionId, days, startTime, endTime } = params;
			updateUrl(divisionId, days.indexOf(newDay), startTime, endTime);
		},
		[params, updateUrl],
	);

	const handleStartTimeChange = useCallback(
		(newStartTime: string) => {
			if (!params) return;
			const { divisionId, day, days, endTime } = params;
			updateUrl(divisionId, days.indexOf(day), newStartTime, endTime);
		},
		[params, updateUrl],
	);

	const handleEndTimeChange = useCallback(
		(newEndTime: string) => {
			if (!params) return;
			const { divisionId, day, days, startTime } = params;
			updateUrl(divisionId, days.indexOf(day), startTime, newEndTime);
		},
		[params, updateUrl],
	);

	const handleDivisionChange = useCallback(
		(newDivisionId: string) => {
			if (!params) return;
			const { day, days, startTime, endTime } = params;
			updateUrl(newDivisionId, days.indexOf(day), startTime, endTime);
		},
		[params, updateUrl],
	);

	const handleMatchClick = useCallback(
		(matchId: string) => {
			setFocusedMatchId(matchId === focusedMatchId ? null : matchId);
		},
		[focusedMatchId],
	);

	// Show loading state while event data is loading
	if (eventLoading || !params) {
		return (
			<CourtGridPageWrapper>
				<Title>Court Grid</Title>
				<div>Loading...</div>
			</CourtGridPageWrapper>
		);
	}

	const { divisionId, startTime, endTime, day, timeRange, after, before } = params;

	return (
		<CourtGridPageWrapper>
			<Title>Court Grid</Title>

			<TempControls>
				<ControlGroup>
					<Label>Division</Label>
					<Select value={divisionId} onChange={(e) => handleDivisionChange(e.target.value)}>
						{divisionsOptions.map((option) => (
							<option key={option.id} value={option.id}>
								{option.label}
							</option>
						))}
					</Select>
				</ControlGroup>

				<ControlGroup>
					<Label>Day</Label>
					<Input type="date" value={day} onChange={(e) => handleDayChange(e.target.value)} />
				</ControlGroup>

				<ControlGroup>
					<Label>Start Time</Label>
					<Input
						type="time"
						value={startTime}
						min={timeRange.start_time.substring(0, 5)}
						max={timeRange.end_time.substring(0, 5)}
						onChange={(e) => handleStartTimeChange(e.target.value)}
					/>
				</ControlGroup>

				<ControlGroup>
					<Label>End Time</Label>
					<Input
						type="time"
						value={endTime}
						min={timeRange.start_time.substring(0, 5)}
						max={timeRange.end_time.substring(0, 5)}
						onChange={(e) => handleEndTimeChange(e.target.value)}
					/>
				</ControlGroup>

				<ControlGroup>
					<Label>Grid Step (minutes)</Label>
					<Select value={gridStep} onChange={(e) => setGridStep(Number(e.target.value))}>
						<option value={15}>15 minutes</option>
						<option value={30}>30 minutes</option>
						<option value={60}>60 minutes</option>
					</Select>
				</ControlGroup>
			</TempControls>

			<GridContainer>
				<CourtGridBoardWidget
					eswId={eswId!}
					divisionId={divisionId === ALL_DIVISIONS_KEY ? null : divisionId}
					after={after}
					before={before}
					focusedMatchId={focusedMatchId}
					gridStep={gridStep}
					onMatchClick={handleMatchClick}
				/>
			</GridContainer>
		</CourtGridPageWrapper>
	);
};
